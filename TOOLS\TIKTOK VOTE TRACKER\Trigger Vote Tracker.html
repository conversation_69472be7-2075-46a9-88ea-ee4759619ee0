<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trigger Vote Tracker</title>

    <!-- Vue.js CDN -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.prod.js"></script>

    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: rgba(15, 15, 18, 1);
            color: white;
            overflow: hidden;
        }

        .app-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            gap: 10px;
            padding: 10px;
            max-width: 800px;
            margin: 0 auto;
        }

        .panel {
            background-color: rgba(32, 28, 28, 1);
            border-radius: 12px;
            padding: 15px;
        }

        .treeview-container {
            max-height: 300px;
            overflow-y: auto;
            border: 2px solid rgba(64, 64, 64, 1);
            border-radius: 8px;
            padding: 10px;
            background-color: rgba(20, 20, 20, 1);
        }

        .trigger-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px;
            margin: 4px 0;
            background-color: rgba(64, 64, 64, 1);
            border-radius: 6px;
            font-size: 14px;
        }

        .trigger-name {
            font-weight: bold;
            color: #fff;
        }

        .trigger-tags {
            font-size: 12px;
            color: rgba(200, 200, 200, 0.8);
            margin-left: 10px;
        }

        .trigger-votes {
            background-color: rgba(256, 64, 88, 1);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }

        .input-group {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }

        .input-field {
            background-color: transparent;
            border: 2px solid rgba(64, 64, 64, 1);
            border-radius: 8px;
            padding: 8px;
            color: white;
            font-size: 14px;
            flex: 1;
        }

        .input-field:focus {
            outline: none;
            border-color: rgba(256, 64, 88, 1);
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: background-color 0.2s;
        }

        .btn-primary {
            background-color: rgba(256, 64, 88, 1);
            color: white;
        }

        .btn-primary:hover {
            background-color: rgba(220, 50, 75, 1);
        }

        .btn-danger {
            background-color: rgba(220, 38, 38, 1);
            color: white;
        }

        .btn-danger:hover {
            background-color: rgba(185, 28, 28, 1);
        }

        .btn-secondary {
            background-color: rgba(64, 64, 64, 1);
            color: white;
        }

        .btn-secondary:hover {
            background-color: rgba(80, 80, 80, 1);
        }

        /* Visual Ranking Display */
        .ranking-display {
            background: transparent;
            padding: 20px;
            border-radius: 12px;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.5s ease, visibility 0.5s ease;
        }

        .ranking-display.visible {
            opacity: 1;
            visibility: visible;
        }

        .ranking-container {
            position: relative;
            min-height: 220px; /* Reserve space for 3 bars with no cutoff */
            max-height: 220px; /* Prevent expansion beyond 3 bars */
            overflow: hidden;
        }

        .ranking-bar {
            background-color: rgba(0, 0, 0, 0.8);
            border-radius: 25px;
            padding: 15px 20px;
            margin: 8px 0;
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 16px;
            font-weight: bold;
            color: white;
            min-height: 20px;
            max-height: 50px; /* Prevent expansion */
            width: 50%; /* Make bars 50% shorter */
            transform: translateX(-100%);
            opacity: 0;
            transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            box-sizing: border-box; /* Ensure consistent sizing */
        }

        .ranking-bar.visible {
            transform: translateX(0);
            opacity: 1;
        }

        .ranking-bar.hidden {
            transform: translateX(-100%);
            opacity: 0;
        }

        .ranking-bar.swapping {
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .ranking-bar:hover {
            background-color: rgba(0, 0, 0, 0.9);
            transform: translateX(5px);
        }

        .ranking-bar.slide-in:nth-child(1) {
            animation-delay: 0.1s;
        }

        .ranking-bar.slide-in:nth-child(2) {
            animation-delay: 0.3s;
        }

        .ranking-bar.slide-in:nth-child(3) {
            animation-delay: 0.5s;
        }

        /* Keyframe animations for better control */
        @keyframes slideInFromLeft {
            from {
                transform: translateX(-100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOutToLeft {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(-100%);
                opacity: 0;
            }
        }

        .ranking-bar.animate-in {
            animation: slideInFromLeft 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
        }

        .ranking-bar.animate-out {
            animation: slideOutToLeft 0.4s cubic-bezier(0.55, 0.055, 0.675, 0.19) forwards;
        }

        /* Vue transition-group animations */
        .ranking-enter-active {
            animation: slideInFromLeft 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .ranking-leave-active {
            animation: slideOutToLeft 0.3s cubic-bezier(0.55, 0.055, 0.675, 0.19);
            position: absolute;
            width: 50%;
        }

        .ranking-leave-to {
            opacity: 0;
            transform: translateX(-100%);
        }

        .ranking-move {
            transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        /* Ensure only 3 slots maximum */
        .ranking-display .ranking-bar:nth-child(n+4) {
            display: none !important;
        }

        .rank-number {
            background-color: rgba(64, 64, 64, 0.8);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
            flex-shrink: 0;
            transition: background-color 0.3s ease;
        }

        .trigger-name-display {
            flex: 1;
            text-align: left;
            font-size: 20px; /* Increased from 16px to 20px (+4) */
        }

        .vote-count {
            background-color: rgba(64, 64, 64, 0.8);
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 14px;
        }

        .panel-header {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
        }

        .panel-title {
            margin: 0;
        }

        .panel-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .connection-led {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #ef4444; /* Default red */
            box-shadow: 0 0 8px rgba(239, 68, 68, 0.6);
            transition: all 0.3s ease;
        }

        .connection-led.connected {
            background-color: #10b981;
            box-shadow: 0 0 8px rgba(16, 185, 129, 0.6);
        }

        .connection-led.connecting {
            background-color: #f59e0b;
            box-shadow: 0 0 8px rgba(245, 158, 11, 0.6);
            animation: pulse-led 1.5s infinite;
        }

        @keyframes pulse-led {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .settings-button {
            width: 36px;
            height: 36px;
            background-color: rgba(64, 64, 64, 1);
            border: none;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            transition: background-color 0.2s;
        }

        .settings-button:hover {
            background-color: rgba(80, 80, 80, 1);
        }

        .settings-popup {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: rgba(32, 28, 28, 1);
            border-radius: 12px;
            padding: 20px;
            width: 400px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
            z-index: 1000;
            border: 1px solid rgba(64, 64, 64, 1);
        }

        .settings-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 999;
        }

        .settings-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .settings-title {
            font-size: 18px;
            font-weight: bold;
            color: white;
        }

        .close-button {
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .close-button:hover {
            color: rgba(256, 64, 88, 1);
        }

        .setting-item {
            margin-bottom: 20px;
        }

        .setting-label {
            display: block;
            color: white;
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .setting-description {
            color: rgba(200, 200, 200, 0.8);
            font-size: 12px;
            margin-bottom: 10px;
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(64, 64, 64, 1);
            transition: 0.3s;
            border-radius: 24px;
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: 0.3s;
            border-radius: 50%;
        }

        input:checked + .toggle-slider {
            background-color: rgba(256, 64, 88, 1);
        }

        input:checked + .toggle-slider:before {
            transform: translateX(26px);
        }

        .keybind-input {
            background-color: rgba(64, 64, 64, 1);
            border: 2px solid rgba(64, 64, 64, 1);
            border-radius: 8px;
            padding: 10px 16px;
            color: white;
            font-size: 14px;
            width: 100%;
            text-align: center;
            font-weight: bold;
            box-sizing: border-box;
            line-height: 1.2;
        }

        .keybind-input:focus {
            outline: none;
            border-color: rgba(256, 64, 88, 1);
        }

        .keybind-input.recording {
            border-color: rgba(256, 64, 88, 1);
            background-color: rgba(256, 64, 88, 0.1);
        }
    </style>
</head>
<body>
    <div id="app" class="app-container">
        <!-- Trigger Management Panel -->
        <div class="panel">
            <div class="panel-header">
                <h3 class="panel-title">Trigger Management</h3>
                <div class="panel-controls">
                    <div
                        class="connection-led"
                        :class="{
                            connected: status === 'Connected',
                            connecting: status === 'Connecting'
                        }"
                        :title="'Status: ' + status"
                    ></div>
                    <button @click="showSettings = true" class="settings-button" title="Settings">
                        ⚙️
                    </button>
                </div>
            </div>

            <!-- Filter Input -->
            <div class="input-group" style="margin-bottom: 15px;">
                <input
                    v-model="filterText"
                    class="input-field"
                    placeholder="🔍 Filter triggers by name or tags..."
                    style="flex: 1;"
                />
                <button
                    v-if="filterText"
                    @click="clearFilter"
                    class="btn btn-secondary"
                    style="padding: 8px 12px;"
                >
                    ✕
                </button>
            </div>

            <!-- Add New Trigger -->
            <div class="input-group">
                <input
                    v-model="newTriggerName"
                    class="input-field"
                    placeholder="Trigger display name..."
                    @keyup.enter="addTrigger"
                />
                <input
                    v-model="newTriggerTags"
                    class="input-field"
                    placeholder="Tags (comma separated)..."
                    @keyup.enter="addTrigger"
                />
                <button @click="addTrigger" class="btn btn-primary">Add</button>
            </div>

            <!-- Triggers TreeView -->
            <div class="treeview-container">
                <div v-if="triggers.length === 0" style="text-align: center; color: rgba(200, 200, 200, 0.6); padding: 20px;">
                    No triggers defined. Add one above to get started.
                </div>
                <div v-if="triggers.length > 0 && filteredTriggers.length === 0" style="text-align: center; color: rgba(200, 200, 200, 0.6); padding: 20px;">
                    No triggers match filter "{{ filterText }}"
                </div>
                <div v-for="(trigger, index) in filteredTriggers" :key="trigger.id" class="trigger-item">
                    <!-- Normal View -->
                    <div v-if="editingTrigger !== trigger.id" style="flex: 1;">
                        <span class="trigger-name">{{ trigger.name }}</span>
                        <span class="trigger-tags">[{{ trigger.tags.join(', ') }}]</span>
                    </div>
                    <div v-if="editingTrigger !== trigger.id" style="display: flex; align-items: center; gap: 10px;">
                        <span class="trigger-votes">{{ trigger.votes }}</span>
                        <button @click="startEdit(trigger)" class="btn btn-secondary" style="padding: 4px 8px; font-size: 12px;">✏️</button>
                        <button @click="removeTrigger(getOriginalIndex(trigger))" class="btn btn-danger" style="padding: 4px 8px; font-size: 12px;">×</button>
                    </div>

                    <!-- Edit View -->
                    <div v-if="editingTrigger === trigger.id" style="flex: 1; display: flex; gap: 8px;">
                        <input v-model="editName" class="input-field" style="flex: 1; padding: 4px 8px; font-size: 12px;" placeholder="Name..."/>
                        <input v-model="editTags" class="input-field" style="flex: 1; padding: 4px 8px; font-size: 12px;" placeholder="Tags..."/>
                    </div>
                    <div v-if="editingTrigger === trigger.id" style="display: flex; align-items: center; gap: 8px;">
                        <button @click="saveEdit(trigger)" class="btn btn-primary" style="padding: 4px 8px; font-size: 12px;">✓</button>
                        <button @click="cancelEdit()" class="btn btn-secondary" style="padding: 4px 8px; font-size: 12px;">✗</button>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div style="display: flex; gap: 8px; margin-top: 10px;">
                <button @click="resetTopTrigger" class="btn btn-secondary" style="flex: 1;">
                    Reset Top Votes
                </button>
                <button @click="exportTriggers" class="btn btn-primary" style="flex: 1;">
                    📤 Export
                </button>
                <button @click="triggerImport" class="btn btn-primary" style="flex: 1;">
                    📥 Import
                </button>
            </div>

            <!-- Hidden file input for import -->
            <input
                ref="fileInput"
                type="file"
                accept=".json"
                @change="importTriggers"
                style="display: none;"
            />
        </div>

        <!-- Visual Ranking Display -->
        <div class="ranking-display" :class="{ visible: hasAnyVotes }">
            <div class="ranking-container">
                <transition-group name="ranking" tag="div" mode="out-in">
                    <div
                        v-for="(trigger, index) in displayedTriggers"
                        :key="trigger.id"
                        class="ranking-bar visible"
                        :class="{
                            'swapping': isSwapping
                        }"
                        :style="{
                            transitionDelay: (index * 0.1) + 's',
                            zIndex: 100 - index
                        }"
                        :data-trigger-id="trigger.id"
                        v-show="index < 3"
                    >
                        <div class="rank-number">{{ index + 1 }}</div>
                        <div class="trigger-name-display">{{ trigger.name }}</div>
                        <div class="vote-count">({{ trigger.votes }})</div>
                    </div>
                </transition-group>
            </div>
        </div>



        <!-- Settings Popup -->
        <div v-if="showSettings" class="settings-overlay" @click="showSettings = false"></div>
        <div v-if="showSettings" class="settings-popup">
            <div class="settings-header">
                <div class="settings-title">Settings</div>
                <button @click="showSettings = false" class="close-button">×</button>
            </div>

            <div class="setting-item">
                <label class="setting-label">Followers Only</label>
                <div class="setting-description">Only accept votes from followers instead of all chatters</div>
                <label class="toggle-switch">
                    <input type="checkbox" v-model="followersOnly" @change="saveSettings">
                    <span class="toggle-slider"></span>
                </label>
            </div>

            <div class="setting-item">
                <label class="setting-label">Reset Votes Keybind</label>
                <div class="setting-description">Press a key combination to reset the top votes</div>
                <input
                    type="text"
                    class="keybind-input"
                    :class="{ recording: recordingKeybind }"
                    :value="keybindDisplay"
                    @focus="startKeybindRecording"
                    @blur="stopKeybindRecording"
                    @keydown="recordKeybind"
                    placeholder="Click to set keybind"
                    readonly
                />
            </div>
        </div>
    </div>

    <script>
        const { createApp } = Vue;

        const app = createApp({
            data() {
                return {
                    status: 'Disconnected',
                    triggers: [],
                    newTriggerName: '',
                    newTriggerTags: '',
                    userVotes: {}, // Track votes by userId
                    websocket: null,
                    editingTrigger: null, // Track which trigger is being edited
                    editName: '',
                    editTags: '',
                    filterText: '', // Filter for treeview
                    isSwapping: false, // Track when positions are swapping
                    previousOrder: [], // Track previous trigger order for animations
                    showSettings: false, // Settings popup visibility
                    followersOnly: false, // Followers only mode
                    keybind: '', // Stored keybind
                    keybindDisplay: '', // Display text for keybind
                    recordingKeybind: false // Whether we're recording a keybind
                };
            },
            computed: {
                sortedTriggers() {
                    // Only include triggers with votes > 0, then sort by votes
                    const triggersWithVotes = this.triggers.filter(trigger => trigger.votes > 0);
                    const sorted = triggersWithVotes.sort((a, b) => b.votes - a.votes);

                    // Check if order changed for animation
                    const currentOrder = sorted.map(t => t.id);
                    if (this.previousOrder.length > 0 &&
                        JSON.stringify(currentOrder) !== JSON.stringify(this.previousOrder)) {
                        this.triggerSwapAnimation();
                    }
                    this.previousOrder = currentOrder;

                    return sorted;
                },
                filteredTriggers() {
                    if (!this.filterText.trim()) {
                        return this.triggers;
                    }

                    const filter = this.filterText.toLowerCase().trim();
                    return this.triggers.filter(trigger => {
                        // Check if filter matches trigger name
                        const nameMatch = trigger.name.toLowerCase().includes(filter);

                        // Check if filter matches any tag
                        const tagMatch = trigger.tags.some(tag =>
                            tag.toLowerCase().includes(filter)
                        );

                        return nameMatch || tagMatch;
                    });
                },
                hasAnyVotes() {
                    return this.triggers.some(trigger => trigger.votes > 0);
                },
                displayedTriggers() {
                    // Get triggers with votes > 0, sorted by votes
                    const triggersWithVotes = this.triggers
                        .filter(trigger => trigger.votes > 0)
                        .sort((a, b) => b.votes - a.votes);

                    // Strictly limit to top 3 only
                    const topThree = triggersWithVotes.slice(0, 3);

                    // Return exactly 3 or fewer triggers
                    return topThree.map((trigger, index) => ({
                        ...trigger,
                        isVisible: true,
                        rank: index + 1
                    }));
                }
            },
            mounted() {
                this.setupWebSocket();
                this.loadTriggers();
                this.loadSettings();
                this.setupGlobalKeybind();
            },
            watch: {
                sortedTriggers: {
                    handler(newTriggers, oldTriggers) {
                        // Trigger animations when rankings change
                        if (oldTriggers && oldTriggers.length > 0) {
                            this.handleRankingChange(newTriggers, oldTriggers);
                        }
                    },
                    deep: true
                }
            },
            methods: {
                addTrigger() {
                    if (this.newTriggerName.trim() && this.newTriggerTags.trim()) {
                        const tags = this.newTriggerTags.split(',').map(tag => tag.trim().toLowerCase()).filter(tag => tag);
                        
                        this.triggers.push({
                            id: Date.now(),
                            name: this.newTriggerName.trim(),
                            tags: tags,
                            votes: 0
                        });
                        
                        this.newTriggerName = '';
                        this.newTriggerTags = '';
                        this.saveTriggerDefinitions();
                    }
                },
                
                removeTrigger(index) {
                    this.triggers.splice(index, 1);
                    this.saveTriggerDefinitions();
                },

                startEdit(trigger) {
                    this.editingTrigger = trigger.id;
                    this.editName = trigger.name;
                    this.editTags = trigger.tags.join(', ');
                },

                saveEdit(trigger) {
                    if (this.editName.trim() && this.editTags.trim()) {
                        trigger.name = this.editName.trim();
                        trigger.tags = this.editTags.split(',').map(tag => tag.trim().toLowerCase()).filter(tag => tag);
                        this.cancelEdit();
                        this.saveTriggerDefinitions();
                    }
                },

                cancelEdit() {
                    this.editingTrigger = null;
                    this.editName = '';
                    this.editTags = '';
                },

                clearFilter() {
                    this.filterText = '';
                },

                getOriginalIndex(trigger) {
                    // Find the original index in the unfiltered triggers array
                    return this.triggers.findIndex(t => t.id === trigger.id);
                },

                triggerSwapAnimation() {
                    // Trigger swap animation
                    this.isSwapping = true;
                    setTimeout(() => {
                        this.isSwapping = false;
                    }, 400); // Match the CSS transition duration
                },

                handleRankingChange(newTriggers, oldTriggers) {
                    // Check if any triggers entered or left the top 3
                    const newIds = newTriggers.map(t => t.id);
                    const oldIds = oldTriggers.map(t => t.id);

                    const entered = newIds.filter(id => !oldIds.includes(id));
                    const left = oldIds.filter(id => !newIds.includes(id));

                    if (entered.length > 0 || left.length > 0) {
                        // Force re-render with animation
                        this.$nextTick(() => {
                            this.triggerSwapAnimation();
                        });
                    }
                },

                getSwapPosition(triggerId) {
                    // Calculate position offset for swapping animation
                    return 0;
                },

                isExactWordMatch(message, tag) {
                    // Convert both to lowercase for case-insensitive comparison
                    const lowerMessage = message.toLowerCase();
                    const lowerTag = tag.toLowerCase();

                    // Create regex pattern for exact word match
                    // \b ensures word boundaries (start/end of word)
                    const wordPattern = new RegExp(`\\b${this.escapeRegex(lowerTag)}\\b`);

                    return wordPattern.test(lowerMessage);
                },

                escapeRegex(string) {
                    // Escape special regex characters in the tag
                    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                },
                
                resetTopTrigger() {
                    if (this.sortedTriggers.length > 0) {
                        const topTrigger = this.sortedTriggers[0];
                        const triggerIndex = this.triggers.findIndex(t => t.id === topTrigger.id);
                        if (triggerIndex !== -1) {
                            this.triggers[triggerIndex].votes = 0;
                            // Remove all user votes for this trigger
                            Object.keys(this.userVotes).forEach(userId => {
                                if (this.userVotes[userId] === topTrigger.id) {
                                    delete this.userVotes[userId];
                                }
                            });
                            // Don't save votes, they reset each stream
                        }
                    }
                },
                
                handleChatMessage(data) {
                    if (data.event === 'chat' && data.data && data.data.comment && data.data.uniqueId) {
                        const message = data.data.comment.toLowerCase().trim();
                        const userId = data.data.uniqueId;
                        const followRole = data.data.followRole || 0; // 0 = none; 1 = follower; 2 = friends

                        // Check followers only setting
                        if (this.followersOnly && followRole === 0) {
                            return; // Skip non-followers if followers only mode is enabled
                        }

                        // Check if message matches any trigger tags (exact word match)
                        for (const trigger of this.triggers) {
                            for (const tag of trigger.tags) {
                                if (this.isExactWordMatch(message, tag)) {
                                    this.processVote(userId, trigger.id);
                                    return; // Only process first match
                                }
                            }
                        }
                    }
                },
                
                processVote(userId, triggerId) {
                    // Remove previous vote if exists
                    if (this.userVotes[userId]) {
                        const previousTriggerId = this.userVotes[userId];
                        const previousTrigger = this.triggers.find(t => t.id === previousTriggerId);
                        if (previousTrigger && previousTrigger.votes > 0) {
                            previousTrigger.votes--;
                        }
                    }
                    
                    // Add new vote
                    this.userVotes[userId] = triggerId;
                    const trigger = this.triggers.find(t => t.id === triggerId);
                    if (trigger) {
                        trigger.votes++;
                    }
                    
                    // Don't save votes, they reset each stream
                },
                
                saveTriggerDefinitions() {
                    // Only save trigger definitions (name + tags), not votes
                    const triggerDefinitions = this.triggers.map(trigger => ({
                        id: trigger.id,
                        name: trigger.name,
                        tags: trigger.tags
                    }));
                    localStorage.setItem('triggerVoteTracker_definitions', JSON.stringify(triggerDefinitions));
                },

                loadTriggers() {
                    // Load trigger definitions and reset all votes to 0
                    const saved = localStorage.getItem('triggerVoteTracker_definitions');
                    let triggerDefinitions = [];

                    if (saved) {
                        triggerDefinitions = JSON.parse(saved);
                    } else if (SAVED_TRIGGERS.length > 0) {
                        // Fallback to built-in triggers if no saved data
                        triggerDefinitions = SAVED_TRIGGERS;
                    }

                    this.triggers = triggerDefinitions.map(def => ({
                        ...def,
                        votes: 0 // Always start with 0 votes for fresh stream
                    }));

                    // Always start with empty user votes for fresh stream
                    this.userVotes = {};
                },

                exportTriggers() {
                    // Create export data
                    const exportData = {
                        version: "1.0",
                        timestamp: new Date().toISOString(),
                        triggers: this.triggers.map(trigger => ({
                            id: trigger.id,
                            name: trigger.name,
                            tags: trigger.tags
                        }))
                    };

                    // Create and download file
                    const dataStr = JSON.stringify(exportData, null, 2);
                    const dataBlob = new Blob([dataStr], { type: 'application/json' });
                    const url = URL.createObjectURL(dataBlob);

                    const link = document.createElement('a');
                    link.href = url;
                    link.download = `trigger-vote-tracker-${new Date().toISOString().split('T')[0]}.json`;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    URL.revokeObjectURL(url);

                    alert(`Exported ${exportData.triggers.length} triggers to file!`);
                },

                triggerImport() {
                    // Trigger the hidden file input
                    this.$refs.fileInput.click();
                },

                importTriggers(event) {
                    const file = event.target.files[0];
                    if (!file) return;

                    const reader = new FileReader();
                    reader.onload = (e) => {
                        try {
                            const importData = JSON.parse(e.target.result);

                            // Validate import data
                            if (!importData.triggers || !Array.isArray(importData.triggers)) {
                                throw new Error('Invalid file format: missing triggers array');
                            }

                            // Validate each trigger
                            const validTriggers = importData.triggers.filter(trigger => {
                                return trigger.name && trigger.tags && Array.isArray(trigger.tags);
                            });

                            if (validTriggers.length === 0) {
                                throw new Error('No valid triggers found in file');
                            }

                            // Ask user if they want to replace or merge
                            const replace = confirm(
                                `Found ${validTriggers.length} triggers in file.\n\n` +
                                `Click OK to REPLACE current triggers\n` +
                                `Click Cancel to MERGE with current triggers`
                            );

                            if (replace) {
                                // Replace all triggers
                                this.triggers = validTriggers.map(trigger => ({
                                    ...trigger,
                                    id: trigger.id || Date.now() + Math.random(),
                                    votes: 0
                                }));
                            } else {
                                // Merge triggers (avoid duplicates by name)
                                const existingNames = this.triggers.map(t => t.name.toLowerCase());
                                const newTriggers = validTriggers.filter(trigger =>
                                    !existingNames.includes(trigger.name.toLowerCase())
                                );

                                newTriggers.forEach(trigger => {
                                    this.triggers.push({
                                        ...trigger,
                                        id: Date.now() + Math.random(),
                                        votes: 0
                                    });
                                });

                                alert(`Merged ${newTriggers.length} new triggers (${validTriggers.length - newTriggers.length} duplicates skipped)`);
                            }

                            // Save the updated triggers
                            this.saveTriggerDefinitions();

                            // Clear user votes since triggers changed
                            this.userVotes = {};

                            if (replace) {
                                alert(`Successfully imported ${validTriggers.length} triggers!`);
                            }

                        } catch (error) {
                            alert(`Import failed: ${error.message}`);
                        }

                        // Reset file input
                        event.target.value = '';
                    };

                    reader.readAsText(file);
                },
                
                updateStatus(newStatus) {
                    this.status = newStatus;
                },
                
                setupWebSocket() {
                    this.connect();
                },
                
                connect() {
                    if (this.websocket) return;

                    this.websocket = new WebSocket("ws://localhost:21213/");

                    this.websocket.onopen = () => {
                        this.updateStatus("Connected");
                    };

                    this.websocket.onclose = () => {
                        this.updateStatus("Disconnected");
                        this.websocket = null;
                        setTimeout(() => this.connect(), 1000);
                    };

                    this.websocket.onerror = () => {
                        this.updateStatus("Connection Failed");
                        this.websocket = null;
                        setTimeout(() => this.connect(), 1000);
                    };

                    this.websocket.onmessage = (event) => {
                        let parsedData = JSON.parse(event.data);
                        console.log("Data received", parsedData);
                        this.handleChatMessage(parsedData);
                    };
                },

                saveSettings() {
                    const settings = {
                        followersOnly: this.followersOnly,
                        keybind: this.keybind,
                        keybindDisplay: this.keybindDisplay
                    };
                    localStorage.setItem('triggerVoteTracker_settings', JSON.stringify(settings));
                },

                loadSettings() {
                    const saved = localStorage.getItem('triggerVoteTracker_settings');
                    if (saved) {
                        const settings = JSON.parse(saved);
                        this.followersOnly = settings.followersOnly || false;
                        this.keybind = settings.keybind || '';
                        this.keybindDisplay = settings.keybindDisplay || '';
                    }
                },

                startKeybindRecording() {
                    this.recordingKeybind = true;
                    this.keybindDisplay = 'Press keys...';
                },

                stopKeybindRecording() {
                    this.recordingKeybind = false;
                    if (!this.keybind) {
                        this.keybindDisplay = '';
                    }
                },

                recordKeybind(event) {
                    if (!this.recordingKeybind) return;

                    event.preventDefault();

                    const keys = [];

                    // Add modifiers in consistent order
                    if (event.ctrlKey) keys.push('Ctrl');
                    if (event.altKey) keys.push('Alt');
                    if (event.shiftKey) keys.push('Shift');
                    if (event.metaKey) keys.push('Meta');

                    // Handle main key with better detection
                    let mainKey = this.normalizeKey(event.key, event.code);
                    if (mainKey && !['Control', 'Alt', 'Shift', 'Meta'].includes(event.key)) {
                        keys.push(mainKey);
                    }

                    // Only save if we have at least one modifier + main key, or a special key
                    if (keys.length >= 2 || (keys.length === 1 && this.isSpecialKey(mainKey))) {
                        this.keybind = keys.join('+');
                        this.keybindDisplay = this.keybind;
                        this.recordingKeybind = false;
                        this.saveSettings();

                        // Remove focus from input
                        event.target.blur();

                        console.log('Keybind recorded:', this.keybind);
                    }
                },

                normalizeKey(key, code) {
                    // Handle numpad keys specifically
                    if (code && code.startsWith('Numpad')) {
                        return code; // Use code for numpad (e.g., "Numpad3")
                    }

                    // Handle other special keys
                    const keyMap = {
                        ' ': 'Space',
                        'ArrowUp': 'Up',
                        'ArrowDown': 'Down',
                        'ArrowLeft': 'Left',
                        'ArrowRight': 'Right',
                        'Escape': 'Esc',
                        'Delete': 'Del',
                        'Insert': 'Ins'
                    };

                    if (keyMap[key]) {
                        return keyMap[key];
                    }

                    // For regular keys, use uppercase
                    if (key.length === 1) {
                        return key.toUpperCase();
                    }

                    return key;
                },

                isSpecialKey(key) {
                    const specialKeys = ['F1', 'F2', 'F3', 'F4', 'F5', 'F6', 'F7', 'F8', 'F9', 'F10', 'F11', 'F12',
                                       'Space', 'Enter', 'Tab', 'Esc', 'Del', 'Ins', 'Home', 'End', 'PageUp', 'PageDown',
                                       'Up', 'Down', 'Left', 'Right'];
                    return specialKeys.includes(key) || key.startsWith('Numpad');
                },

                setupGlobalKeybind() {
                    document.addEventListener('keydown', (event) => {
                        if (!this.keybind) return;

                        // Don't trigger if user is typing in an input field
                        if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
                            return;
                        }

                        const pressedKeys = [];

                        // Add modifiers in same order as recording
                        if (event.ctrlKey) pressedKeys.push('Ctrl');
                        if (event.altKey) pressedKeys.push('Alt');
                        if (event.shiftKey) pressedKeys.push('Shift');
                        if (event.metaKey) pressedKeys.push('Meta');

                        // Use same key normalization as recording
                        let mainKey = this.normalizeKey(event.key, event.code);
                        if (mainKey && !['Control', 'Alt', 'Shift', 'Meta'].includes(event.key)) {
                            pressedKeys.push(mainKey);
                        }

                        const pressedKeybind = pressedKeys.join('+');

                        if (pressedKeybind === this.keybind) {
                            event.preventDefault();
                            console.log('Keybind triggered:', pressedKeybind);
                            this.resetTopTrigger();
                        }
                    });
                }
            }
        }).mount('#app');
    </script>
</body>
</html>
