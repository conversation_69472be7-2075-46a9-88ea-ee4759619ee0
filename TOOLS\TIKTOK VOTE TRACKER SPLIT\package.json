{"name": "tiktok-vote-tracker-split", "version": "1.0.0", "description": "Split TikTok Vote Tracker with Management App and Overlay", "main": "management-app/main.js", "scripts": {"start": "electron management-app/main.js", "start-management": "electron management-app/main.js", "dev": "electron management-app/main.js --dev", "build": "electron-builder", "postinstall": "electron-builder install-app-deps"}, "keywords": ["tiktok", "vote", "tracker", "electron", "overlay"], "author": "TikTok Vote Tracker", "license": "MIT", "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4"}, "dependencies": {"cors": "^2.8.5", "express": "^4.21.2", "ws": "^8.18.3"}, "build": {"appId": "com.tiktok.vote.tracker", "productName": "TikTok Vote Tracker", "directories": {"output": "dist"}, "files": ["management-app/**/*", "overlay.html", "package.json"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}}