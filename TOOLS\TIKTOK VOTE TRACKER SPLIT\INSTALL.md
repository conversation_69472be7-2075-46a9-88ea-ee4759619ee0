# Quick Installation Guide

## Prerequisites
- Node.js (version 16 or higher)
- npm (comes with Node.js)

## Installation Steps

1. **Open Terminal/Command Prompt**
   - Navigate to the project folder:
   ```bash
   cd "TOOLS\TIKTOK VOTE TRACKER SPLIT"
   ```

2. **Install Dependencies**
   ```bash
   npm install
   ```

3. **Start the Application**
   
   **Option A: Use the startup script (Windows)**
   ```bash
   start.bat
   ```
   
   **Option B: Manual start**
   ```bash
   npm start
   ```

4. **Open Overlay**
   - The overlay will open automatically with the startup script
   - Or manually open `overlay.html` in your browser
   - For OBS: Add Browser Source with file path to `overlay.html`

## Quick Test

1. Open `test-api.html` in your browser to verify the API is working
2. Add some triggers in the Management App
3. The overlay should show the ranking in real-time

## Troubleshooting

- **"npm not found"**: Install Node.js from https://nodejs.org/
- **Port 3001 in use**: Close other applications using this port
- **Overlay shows nothing**: Ensure Management App is running first

## File Locations

- **Management App**: Electron window that opens automatically
- **Overlay**: `overlay.html` (use this file path in OBS)
- **API**: http://localhost:3001/api/votes
- **Original**: `TOOLS\TIKTOK VOTE TRACKER\Trigger Vote Tracker.html` (unchanged)
