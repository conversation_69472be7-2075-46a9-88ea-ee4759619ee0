<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TikTok Vote Tracker - Overlay</title>

    <!-- Vue.js CDN -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.prod.js"></script>

    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: transparent;
            color: white;
            overflow: hidden;
        }

        .overlay-container {
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            padding: 20px;
            box-sizing: border-box;
        }

        /* Visual Ranking Display */
        .ranking-display {
            background: transparent;
            padding: 20px;
            border-radius: 12px;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.5s ease, visibility 0.5s ease;
        }

        .ranking-display.visible {
            opacity: 1;
            visibility: visible;
        }

        .ranking-container {
            position: relative;
            min-height: 220px; /* Reserve space for 3 bars with no cutoff */
            max-height: 220px; /* Prevent expansion beyond 3 bars */
            overflow: hidden;
        }

        .ranking-bar {
            background-color: rgba(0, 0, 0, 0.8);
            border-radius: 25px;
            padding: 15px 20px;
            margin: 8px 0;
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 16px;
            font-weight: bold;
            color: white;
            min-height: 20px;
            max-height: 50px; /* Prevent expansion */
            width: 50%; /* Make bars 50% shorter */
            transform: translateX(-100%);
            opacity: 0;
            transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            box-sizing: border-box; /* Ensure consistent sizing */
        }

        .ranking-bar.visible {
            transform: translateX(0);
            opacity: 1;
        }

        .ranking-bar.hidden {
            transform: translateX(-100%);
            opacity: 0;
        }

        .ranking-bar.swapping {
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .ranking-bar:hover {
            background-color: rgba(0, 0, 0, 0.9);
            transform: translateX(5px);
        }

        .ranking-bar.slide-in:nth-child(1) {
            animation-delay: 0.1s;
        }

        .ranking-bar.slide-in:nth-child(2) {
            animation-delay: 0.3s;
        }

        .ranking-bar.slide-in:nth-child(3) {
            animation-delay: 0.5s;
        }

        /* Keyframe animations for better control */
        @keyframes slideInFromLeft {
            from {
                transform: translateX(-100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOutToLeft {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(-100%);
                opacity: 0;
            }
        }

        .ranking-bar.animate-in {
            animation: slideInFromLeft 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
        }

        .ranking-bar.animate-out {
            animation: slideOutToLeft 0.4s cubic-bezier(0.55, 0.055, 0.675, 0.19) forwards;
        }

        /* Vue transition-group animations */
        .ranking-enter-active {
            animation: slideInFromLeft 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .ranking-leave-active {
            animation: slideOutToLeft 0.3s cubic-bezier(0.55, 0.055, 0.675, 0.19);
            position: absolute;
            width: 50%;
        }

        .ranking-leave-to {
            opacity: 0;
            transform: translateX(-100%);
        }

        .ranking-move {
            transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        /* Ensure only 3 slots maximum */
        .ranking-display .ranking-bar:nth-child(n+4) {
            display: none !important;
        }

        .rank-number {
            background-color: rgba(64, 64, 64, 0.8);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
            flex-shrink: 0;
            transition: background-color 0.3s ease;
        }

        .trigger-name-display {
            flex: 1;
            text-align: left;
            font-size: 20px; /* Increased from 16px to 20px (+4) */
        }

        .vote-count {
            background-color: rgba(64, 64, 64, 0.8);
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div id="app" class="overlay-container">
        <!-- Debug indicator (remove this in production) -->
        <div v-if="!hasAnyVotes" style="position: fixed; top: 10px; left: 10px; background: rgba(0,0,0,0.8); color: white; padding: 5px 10px; border-radius: 5px; font-size: 12px; z-index: 1000;">
            Overlay Active - Waiting for votes... ({{ displayedTriggers.length }} triggers)
        </div>

        <!-- Visual Ranking Display -->
        <div class="ranking-display" :class="{ visible: hasAnyVotes }">
            <div class="ranking-container">
                <transition-group name="ranking" tag="div" mode="out-in">
                    <div
                        v-for="(trigger, index) in displayedTriggers"
                        :key="trigger.id"
                        class="ranking-bar visible"
                        :class="{
                            'swapping': isSwapping
                        }"
                        :style="{
                            transitionDelay: (index * 0.1) + 's',
                            zIndex: 100 - index
                        }"
                        :data-trigger-id="trigger.id"
                        v-show="index < 3"
                    >
                        <div class="rank-number">{{ index + 1 }}</div>
                        <div class="trigger-name-display">{{ trigger.name }}</div>
                        <div class="vote-count">({{ trigger.votes }})</div>
                    </div>
                </transition-group>
            </div>
        </div>
    </div>

    <script>
        const { createApp } = Vue;

        const app = createApp({
            data() {
                return {
                    displayedTriggers: [],
                    hasAnyVotes: false,
                    isSwapping: false,
                    previousOrder: [],
                    pollInterval: null
                };
            },
            mounted() {
                this.startPolling();
            },
            beforeUnmount() {
                this.stopPolling();
            },
            methods: {
                startPolling() {
                    // Poll every 500ms
                    this.pollInterval = setInterval(() => {
                        this.fetchVoteData();
                    }, 500);
                    
                    // Initial fetch
                    this.fetchVoteData();
                },
                
                stopPolling() {
                    if (this.pollInterval) {
                        clearInterval(this.pollInterval);
                        this.pollInterval = null;
                    }
                },
                
                async fetchVoteData() {
                    try {
                        const response = await fetch('http://localhost:3001/api/votes');
                        if (response.ok) {
                            const data = await response.json();

                            // Debug logging
                            console.log('Fetched vote data:', data);

                            // Check if order changed for animation
                            const currentOrder = data.triggers.map(t => t.id);
                            if (this.previousOrder.length > 0 &&
                                JSON.stringify(currentOrder) !== JSON.stringify(this.previousOrder)) {
                                this.triggerSwapAnimation();
                            }
                            this.previousOrder = currentOrder;

                            this.displayedTriggers = data.triggers;
                            this.hasAnyVotes = data.hasAnyVotes;

                            console.log('Updated overlay state:', {
                                displayedTriggers: this.displayedTriggers,
                                hasAnyVotes: this.hasAnyVotes
                            });
                        } else {
                            console.error('API response not ok:', response.status, response.statusText);
                        }
                    } catch (error) {
                        console.error('Failed to fetch vote data:', error);
                        // Don't show errors in overlay, just continue polling
                    }
                },
                
                triggerSwapAnimation() {
                    // Trigger swap animation
                    this.isSwapping = true;
                    setTimeout(() => {
                        this.isSwapping = false;
                    }, 400); // Match the CSS transition duration
                }
            }
        }).mount('#app');
    </script>
</body>
</html>
