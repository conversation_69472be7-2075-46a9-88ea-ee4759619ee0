<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Keybind Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #1a1a1a;
            color: white;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
        }
        .keybind-input {
            width: 300px;
            padding: 10px;
            font-size: 16px;
            border: 2px solid #3b82f6;
            border-radius: 5px;
            background-color: #2a2a2a;
            color: white;
        }
        .keybind-input.recording {
            border-color: #ef4444;
            background-color: #3a1a1a;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background-color: #2a2a2a;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Keybind Recording Test</h1>
        <p>Click in the input field below and try pressing <strong>Shift+Ctrl+K</strong></p>
        <p>The old version would stop at "Shift+Ctrl", the new version should wait for the "K"</p>
        
        <input
            type="text"
            class="keybind-input"
            id="keybindInput"
            placeholder="Click here and press Shift+Ctrl+K"
            readonly
        />
        
        <div class="result">
            <h3>Results:</h3>
            <div id="results">Click in the input field above to start testing...</div>
        </div>
    </div>

    <script>
        let recordingKeybind = false;
        let keybindDisplay = '';
        let keybind = '';
        
        const input = document.getElementById('keybindInput');
        const results = document.getElementById('results');
        
        input.addEventListener('focus', () => {
            recordingKeybind = true;
            keybindDisplay = 'Press keys...';
            input.value = keybindDisplay;
            input.classList.add('recording');
            results.innerHTML = 'Recording started... Press Shift+Ctrl+K';
        });
        
        input.addEventListener('blur', () => {
            recordingKeybind = false;
            input.classList.remove('recording');
            if (!keybind) {
                keybindDisplay = '';
                input.value = '';
            }
        });
        
        input.addEventListener('keydown', (event) => {
            if (!recordingKeybind) return;

            event.preventDefault();

            const keys = [];

            // Add modifiers in consistent order
            if (event.ctrlKey) keys.push('Ctrl');
            if (event.altKey) keys.push('Alt');
            if (event.shiftKey) keys.push('Shift');
            if (event.metaKey) keys.push('Meta');

            // Handle main key with better detection
            let mainKey = normalizeKey(event.key, event.code);
            
            // Only process if we have a non-modifier key
            if (mainKey && !['Control', 'Alt', 'Shift', 'Meta'].includes(event.key)) {
                keys.push(mainKey);
                
                // Save the keybind (require at least one modifier + main key, or a special key alone)
                if (keys.length >= 2 || (keys.length === 1 && isSpecialKey(mainKey))) {
                    keybind = keys.join('+');
                    keybindDisplay = keybind;
                    recordingKeybind = false;
                    input.value = keybindDisplay;
                    input.classList.remove('recording');

                    // Remove focus from input
                    input.blur();

                    results.innerHTML = `
                        <strong>✅ Success!</strong><br>
                        Recorded keybind: <strong>${keybind}</strong><br>
                        This should be "Shift+Ctrl+K" if you pressed those keys.
                    `;
                }
            } else {
                // Just show current modifiers while waiting for main key
                if (keys.length > 0) {
                    keybindDisplay = keys.join('+') + '+...';
                    input.value = keybindDisplay;
                    results.innerHTML = `Waiting for main key... Current: <strong>${keybindDisplay}</strong>`;
                }
            }
        });
        
        function normalizeKey(key, code) {
            // Handle numpad keys specifically
            if (code && code.startsWith('Numpad')) {
                return code; // Use code for numpad (e.g., "Numpad3")
            }

            // Handle other special keys
            const keyMap = {
                ' ': 'Space',
                'ArrowUp': 'Up',
                'ArrowDown': 'Down',
                'ArrowLeft': 'Left',
                'ArrowRight': 'Right',
                'Escape': 'Esc',
                'Delete': 'Del',
                'Insert': 'Ins'
            };

            if (keyMap[key]) {
                return keyMap[key];
            }

            // For regular keys, use uppercase
            if (key.length === 1) {
                return key.toUpperCase();
            }

            return key;
        }
        
        function isSpecialKey(key) {
            const specialKeys = ['F1', 'F2', 'F3', 'F4', 'F5', 'F6', 'F7', 'F8', 'F9', 'F10', 'F11', 'F12',
                               'Space', 'Enter', 'Tab', 'Esc', 'Del', 'Ins', 'Home', 'End', 'PageUp', 'PageDown',
                               'Up', 'Down', 'Left', 'Right'];
            return specialKeys.includes(key) || key.startsWith('Numpad');
        }
    </script>
</body>
</html>
