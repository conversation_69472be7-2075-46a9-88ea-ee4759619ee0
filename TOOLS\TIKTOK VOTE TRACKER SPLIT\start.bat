@echo off
echo Starting TikTok Vote Tracker Split...
echo.
echo This will:
echo 1. Start the Management App (Electron)
echo 2. Open the Overlay in your default browser
echo.
echo Press any key to continue...
pause >nul

echo Starting Management App...
start "" npm start

echo Waiting for server to start...
timeout /t 3 /nobreak >nul

echo Opening Overlay...
start "" "%CD%\overlay.html"

echo.
echo Both components are now running!
echo.
echo Management App: Electron window
echo Overlay: Browser window (use this for OBS)
echo API: http://localhost:3001/api/votes
echo.
echo Press any key to exit...
pause >nul
