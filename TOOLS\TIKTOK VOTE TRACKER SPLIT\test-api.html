<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test - TikTok Vote Tracker</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #1a1a1a;
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success { background-color: #10b981; }
        .error { background-color: #ef4444; }
        .warning { background-color: #f59e0b; }
        pre {
            background-color: #2a2a2a;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        button {
            background-color: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #2563eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>TikTok Vote Tracker API Test</h1>
        
        <div id="status" class="status warning">
            Click "Test API" to check if the management app is running...
        </div>
        
        <button onclick="testAPI()">Test API</button>
        <button onclick="startAutoRefresh()">Auto Refresh (1s)</button>
        <button onclick="stopAutoRefresh()">Stop Auto Refresh</button>
        
        <h2>API Response:</h2>
        <pre id="response">No data yet...</pre>
        
        <h2>Instructions:</h2>
        <ol>
            <li>Make sure the Management App is running (npm start)</li>
            <li>Click "Test API" to verify the connection</li>
            <li>Use "Auto Refresh" to see live updates</li>
            <li>Add some triggers in the Management App and vote for them</li>
        </ol>
    </div>

    <script>
        let refreshInterval = null;
        
        async function testAPI() {
            const statusDiv = document.getElementById('status');
            const responseDiv = document.getElementById('response');
            
            try {
                statusDiv.textContent = 'Testing API connection...';
                statusDiv.className = 'status warning';
                
                const response = await fetch('http://localhost:3001/api/votes');
                
                if (response.ok) {
                    const data = await response.json();
                    statusDiv.textContent = `✅ API is working! Found ${data.triggers.length} triggers with votes. Status: ${data.status}`;
                    statusDiv.className = 'status success';
                    responseDiv.textContent = JSON.stringify(data, null, 2);
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                statusDiv.textContent = `❌ API Error: ${error.message}`;
                statusDiv.className = 'status error';
                responseDiv.textContent = `Error: ${error.message}\n\nMake sure the Management App is running:\n1. Open terminal in the project folder\n2. Run: npm start`;
            }
        }
        
        function startAutoRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
            
            refreshInterval = setInterval(testAPI, 1000);
            testAPI(); // Initial call
        }
        
        function stopAutoRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
                refreshInterval = null;
            }
        }
    </script>
</body>
</html>
