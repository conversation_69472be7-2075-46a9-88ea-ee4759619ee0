# TikTok Vote Tracker Split

A split version of the TikTok Vote Tracker with separate management app and overlay functionality.

## Features

### Management App (Electron)
- **Exact same UI/UX** as the original Trigger Vote Tracker.html
- Full trigger management (add, edit, delete, import/export)
- WebSocket connection to TikTok Live chat
- Settings panel with followers-only mode
- **System-wide global keybind** for resetting top votes (works even when app is not focused)
- Serves vote data via REST API at `http://localhost:3001/api/votes`

### Overlay (overlay.html)
- **Lightweight HTML file** that polls the API every 500ms
- Shows only the ranking display without management controls
- **Transparent background** - perfect for OBS overlays
- Same visual animations and styling as the original
- No dependencies on the management app UI

## Setup Instructions

### 1. Install Dependencies
```bash
cd "TOOLS\TIKTOK VOTE TRACKER SPLIT"
npm install
```

### 2. Start the Management App
```bash
npm start
```
or
```bash
npm run start-management
```

### 3. Use the Overlay
Open `overlay.html` in any web browser or add it as a Browser Source in OBS:
- **File Path**: `file:///path/to/overlay.html`
- **URL**: `file:///C:/Users/<USER>/Documents/augment-projects/_TIKTOK_PLUGINS/TOOLS/TIKTOK%20VOTE%20TRACKER%20SPLIT/overlay.html`

## How It Works

1. **Management App**: 
   - Runs as an Electron application
   - Connects to TikTok Live WebSocket (ws://localhost:21213/)
   - Processes chat messages and tracks votes
   - Serves vote data via Express server on port 3001

2. **Overlay**:
   - Polls `http://localhost:3001/api/votes` every 500ms
   - Displays top 3 triggers with votes > 0
   - Updates in real-time with smooth animations

## Global Keybind

The management app supports **system-wide global keybinds** using Electron's globalShortcut API:

1. Open Settings (⚙️ button)
2. Click in the "Reset Votes Keybind" field
3. Press your desired key combination (e.g., Ctrl+R, Alt+F1, etc.)
4. The keybind will work even when the app is not focused

## API Endpoint

### GET /api/votes
Returns current vote data:
```json
{
  "triggers": [
    {
      "id": 123456789,
      "name": "Trigger Name",
      "tags": ["tag1", "tag2"],
      "votes": 5,
      "rank": 1
    }
  ],
  "hasAnyVotes": true,
  "status": "Connected"
}
```

## File Structure

```
TOOLS\TIKTOK VOTE TRACKER SPLIT\
├── package.json                 # Dependencies and scripts
├── overlay.html                 # Standalone overlay file
├── README.md                    # This file
└── management-app\
    ├── main.js                  # Electron main process
    └── index.html               # Management app UI
```

## Original File

The original `TOOLS\TIKTOK VOTE TRACKER\Trigger Vote Tracker.html` remains **completely untouched** and can still be used independently.

## Development

To run in development mode with DevTools:
```bash
npm run dev
```

## Building

To build distributable packages:
```bash
npm run build
```

## Troubleshooting

1. **Overlay shows no data**: Ensure the management app is running and the API is accessible at `http://localhost:3001/api/votes`

2. **Global keybind not working**: Try different key combinations. Some keys may be reserved by the system.

3. **WebSocket connection failed**: Ensure TikTok Live connection tool is running on `ws://localhost:21213/`

4. **Port 3001 in use**: The management app will fail to start if port 3001 is already in use. Close other applications using this port.
