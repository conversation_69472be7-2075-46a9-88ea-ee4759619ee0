const { app, BrowserWindow, globalShortcut, ipcMain } = require('electron');
const path = require('path');
const express = require('express');
const cors = require('cors');
const WebSocket = require('ws');

let mainWindow;
let expressApp;
let server;
let voteData = {
  triggers: [],
  userVotes: {},
  status: 'Disconnected'
};

// Express server for API
function createExpressServer() {
  expressApp = express();
  expressApp.use(cors());
  expressApp.use(express.json());

  // API endpoint for vote data
  expressApp.get('/api/votes', (req, res) => {
    // Calculate displayed triggers (top 3 with votes > 0)
    const triggersWithVotes = voteData.triggers
      .filter(trigger => trigger.votes > 0)
      .sort((a, b) => b.votes - a.votes)
      .slice(0, 3)
      .map((trigger, index) => ({
        ...trigger,
        rank: index + 1
      }));

    res.json({
      triggers: triggersWithVotes,
      hasAnyVotes: triggersWithVotes.length > 0,
      status: voteData.status
    });
  });

  // Start server
  server = expressApp.listen(3001, () => {
    console.log('API server running on http://localhost:3001');
  });
}

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 800,
    height: 900,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true
    },
    icon: path.join(__dirname, 'assets', 'icon.png'),
    title: 'TikTok Vote Tracker - Management'
  });

  mainWindow.loadFile(path.join(__dirname, 'index.html'));

  // Open DevTools in development
  if (process.argv.includes('--dev')) {
    mainWindow.webContents.openDevTools();
  }

  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// IPC handlers for communication with renderer
ipcMain.handle('get-vote-data', () => {
  return voteData;
});

ipcMain.handle('update-vote-data', (event, newData) => {
  voteData = { ...voteData, ...newData };
  return voteData;
});

ipcMain.handle('register-global-shortcut', (event, keybind) => {
  // Unregister previous shortcut
  globalShortcut.unregisterAll();
  
  if (keybind) {
    try {
      const success = globalShortcut.register(keybind, () => {
        // Send reset command to renderer
        if (mainWindow && !mainWindow.isDestroyed()) {
          mainWindow.webContents.send('global-shortcut-triggered');
        }
      });
      
      if (success) {
        console.log('Global shortcut registered:', keybind);
        return { success: true };
      } else {
        console.log('Failed to register global shortcut:', keybind);
        return { success: false, error: 'Failed to register shortcut' };
      }
    } catch (error) {
      console.error('Error registering global shortcut:', error);
      return { success: false, error: error.message };
    }
  }
  
  return { success: true };
});

app.whenReady().then(() => {
  createWindow();
  createExpressServer();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on('window-all-closed', () => {
  // Cleanup
  if (server) {
    server.close();
  }
  globalShortcut.unregisterAll();
  
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('will-quit', () => {
  globalShortcut.unregisterAll();
});
